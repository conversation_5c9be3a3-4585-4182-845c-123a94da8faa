# services/response_service.py

import logging
import time
from langchain_openai import OpenAIEmbeddings
from utils.token_utils import concatenate_answers, calculate_sum, calculate_token_sum, is_not_null_or_not_empty
from utils.pdf_utils import extract_image_urls
from config import OPENAI_API_KEY_ADMIN, llm_config
from langchain.chains import create_history_aware_retriever, create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_community.vectorstores import InMemoryVectorStore
from langchain_core.documents import Document
from llm_manager.llm_factory import LLMFactory
from llm_manager.prompts import (contextualize_q_system_prompt, rag_qa_system_prompt, mcq_exp_sys_prompt,
                                 mcq_qa_sys_prompt, hint_qa_sys_prompt, qa_explanation)
from llm_manager.llm_variables import (huggingface_llm, huggingface_model, openai_admin_llm, openai_admin_model,
                                       openai_llm, openai_model, openai_drive_llm, openai_drive_model)

logger = logging.getLogger(__name__)

llm_factory = LLMFactory(llm_config)


def generate_response_chat(prompt, vector_store, temp_chat_history, custom_prompt, for_book):
    try:
        logger.info("Generating response chat")
        if for_book:
            llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini")
        else:
            llm = llm_factory.get_llm("openai", "gpt-4.1-mini")
        logger.info(f"\n{contextualize_q_system_prompt}")
        contextualize_q_prompt = llm_factory.create_qa_prompt(contextualize_q_system_prompt)
        history_aware_retriever = create_history_aware_retriever(
            llm, vector_store, contextualize_q_prompt
        )

        if is_not_null_or_not_empty(custom_prompt):
            logger.info("Custom prompt used")
            qa_system_prompt = custom_prompt + """
                Template:
                {context}
                """
        else:
            logger.info("Default prompt used")
            qa_system_prompt = rag_qa_system_prompt

        qa_prompt = llm_factory.create_qa_prompt(qa_system_prompt)

        question_answer_chain = create_stuff_documents_chain(llm, qa_prompt)
        rag_chain = create_retrieval_chain(history_aware_retriever, question_answer_chain)

        new_chat_history = [
            HumanMessage(content=item['user']) if key == 'user' else AIMessage(content=item['ai'])
            for item in temp_chat_history for key in item]

        chat_history = new_chat_history

        question = prompt
        ai_msg_1 = rag_chain.invoke({"input": question, "chat_history": chat_history})
        chat_history.extend([HumanMessage(content=question), ai_msg_1["answer"]])

        response = ai_msg_1
        logger.info(f"Response generated:\n {response}")
        return response["answer"]
    except Exception as e:
        logger.error(f"Error generating response: {e}")
        raise e


def generate_response_img(image_url, query):
    llm = llm_factory.get_llm("openai", "gpt-4.1-mini")
    response = llm.invoke(
        [
            HumanMessage(
                content=[
                    {"type": "text", "text": query},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url,
                            "detail": "auto",
                        },
                    },
                ]
            )
        ]
    )

    return response


def generate_mcq_img_response(prompt, que_type, user_query, language):
    question_text, options = extract_image_urls(prompt)
    llm = llm_factory.get_llm("openai", "gpt-4.1-mini")
    sys_prompt = ""
    if que_type == "explain":
        sys_prompt = mcq_exp_sys_prompt
    elif que_type == "similarMCQ":
        sys_prompt = mcq_qa_sys_prompt
    elif que_type == "hint":
        sys_prompt = hint_qa_sys_prompt
    elif que_type == "userInput":
        sys_prompt = f"""{prompt}"""

    res_lang = "English"
    if language is not None and language != "":
        res_lang = language
        sys_prompt += f"""The response should be in {res_lang} language."""
    elif language == "":
        sys_prompt += f"""The response should be in {res_lang} language."""

    content = [{"type": "text", "text": sys_prompt.replace("${prompt}", question_text)}]

    for option in options:
        content.append(
            {
                "type": "text",
                "text": option["text"]
            }
        )
        content.append(
            {
                "type": "image_url",
                "image_url": {
                    "url": option["image_url"],
                    "detail": "auto",
                },
            }
        )
        # Invoke LLM
    response = llm.invoke(
        [HumanMessage(content=content)]
    )
    return {
        "answer": response.content,
        "query": user_query
    }


def generate_response_content_admin(user_type, prompt, res_type, vector_data):
    """
    Function to generate a response content for admin based on a given prompt, res_type, and vector data.
    It uses the ChatPromptTemplate from langchain to create a prompt and the ChatOpenAI to generate the response.

    Args:
        user_type (str): The type of user (admin or drive).
        prompt (str): The prompt string.
        res_type (str): The resType string.
        vector_data (list): The vector data containing text chunks.

    Returns:
        tuple: The concatenated answers, cost summary, token summary, and concatenated answers from the data generation
        operation.
    """
    # Define the cost per token for input and output
    input_cost_per_token = 0.0005 / 1000
    output_cost_per_token = 0.0015 / 1000

    logger.info(f"[DB-DEBUG] Starting generate_response_content_admin for user_type={user_type}, prompt={prompt}, res_type={res_type}")
    logger.info(f"[DB-DEBUG] Vector data type: {type(vector_data)}, length: {len(vector_data) if vector_data else 0}")

    try:
        # Initialize the ChatOpenAI with the specified model and temperature
        llm_type = None
        llm_model = None
        if user_type.lower() == "admin":
            llm_type = openai_admin_llm
            llm_model = openai_admin_model
            logger.info(f"[DB-DEBUG] Using admin LLM: {llm_type}, model: {llm_model}")
        elif user_type.lower() == "drive":
            llm_type = openai_drive_llm
            llm_model = openai_drive_model
            logger.info(f"[DB-DEBUG] Using drive LLM: {llm_type}, model: {llm_model}")
        else:
            logger.warning(f"[DB-DEBUG] Unknown user_type: {user_type}, defaulting to admin")
            llm_type = openai_admin_llm
            llm_model = openai_admin_model

        logger.info(f"[DB-DEBUG] Initializing LLM with type={llm_type}, model={llm_model}")
        start_time = time.time()
        llm = llm_factory.get_llm(llm_type, llm_model)
        elapsed_time = time.time() - start_time
        logger.info(f"[DB-DEBUG] LLM initialized in {elapsed_time:.2f} seconds")

        prompt_template = ChatPromptTemplate.from_template(
            f"""
            You are an AI assistant, answer only from the given context.
            Question : {prompt}
            {{context}}
            """
        )
        logger.info(f"[DB-DEBUG] Prompt template created")

        # Initialize an empty array to store the results
        results_array = []

        if not vector_data:
            logger.warning(f"[DB-DEBUG] Vector data is empty or None")
            # Return empty results if no data
            empty_result = {
                'inputCost': 0,
                'outputCost': 0,
                'totalCost': 0,
                'answer': "No data available to answer this question.",
                'promptTokens': 0,
                'completionTokens': 0,
                'totalTokens': 0,
            }
            results_array.append(empty_result)
        else:
            # Check if we have full text (single string) or chunked data (list of lists)
            if len(vector_data) == 1 and isinstance(vector_data[0], str):
                # Single full text document - make one LLM call
                logger.info(f"[DB-DEBUG] Processing full text document (length: {len(vector_data[0])} characters)")

                try:
                    full_text = vector_data[0]
                    context = full_text
                    context_length = len(context)
                    logger.info(f"[DB-DEBUG] Context created, length: {context_length} characters")

                    # Create a runnable sequence using the prompt and ChatOpenAI llm
                    chain = prompt_template | llm

                    # Invoke the chain and get the completion response
                    logger.info(f"[DB-DEBUG] Invoking LLM chain for full text")
                    start_time = time.time()
                    completion_response = chain.invoke({
                        'context': f'{context}',
                    })
                    elapsed_time = time.time() - start_time
                    logger.info(f"[DB-DEBUG] LLM response received in {elapsed_time:.2f} seconds for full text")

                    # Extract the token usage from the response metadata
                    if 'response_metadata' in completion_response and 'token_usage' in completion_response.response_metadata:
                        token_usage = completion_response.response_metadata['token_usage']
                        prompt_tokens = token_usage.get('prompt_tokens', 0)
                        completion_tokens = token_usage.get('completion_tokens', 0)
                        total_tokens = token_usage.get('total_tokens', 0)

                        logger.info(f"[DB-DEBUG] Token usage - prompt: {prompt_tokens}, completion: {completion_tokens}, total: {total_tokens}")

                        # Calculate the cost for input, output, and total
                        input_cost = prompt_tokens * input_cost_per_token
                        output_cost = completion_tokens * output_cost_per_token
                        total_cost = input_cost + output_cost
                    else:
                        logger.warning(f"[DB-DEBUG] Token usage information not available in response")
                        prompt_tokens = 0
                        completion_tokens = 0
                        total_tokens = 0
                        input_cost = 0
                        output_cost = 0
                        total_cost = 0

                    # Append the result to the results array
                    results_array.append({
                        'inputCost': input_cost,
                        'outputCost': output_cost,
                        'totalCost': total_cost,
                        'answer': completion_response.content,
                        'promptTokens': prompt_tokens,
                        'completionTokens': completion_tokens,
                        'totalTokens': total_tokens,
                    })
                    logger.info(f"[DB-DEBUG] Result added for full text")

                except Exception as e:
                    logger.error(f"[DB-DEBUG] Error processing full text: {str(e)}", exc_info=True)
                    # Add an error result
                    results_array.append({
                        'inputCost': 0,
                        'outputCost': 0,
                        'totalCost': 0,
                        'answer': f"Error processing the data: {str(e)}",
                        'promptTokens': 0,
                        'completionTokens': 0,
                        'totalTokens': 0,
                    })
            else:
                # Multiple chunks - process each batch (legacy behavior)
                logger.info(f"[DB-DEBUG] Processing chunked data with {len(vector_data)} batches")
                for i in range(len(vector_data)):
                    try:
                        batch = vector_data[i]
                        logger.info(f"[DB-DEBUG] Processing batch {i+1}/{len(vector_data)}, batch type: {type(batch)}, length: {len(batch) if batch else 0}")

                        if not batch:
                            logger.warning(f"[DB-DEBUG] Batch {i+1} is empty, skipping")
                            continue

                        # Join the batch into a single context string
                        context = '\n'.join(batch)
                        context_length = len(context)
                        logger.info(f"[DB-DEBUG] Context created, length: {context_length} characters")

                        # Create a runnable sequence using the prompt and ChatOpenAI llm
                        chain = prompt_template | llm

                        # Invoke the chain and get the completion response
                        logger.info(f"[DB-DEBUG] Invoking LLM chain for batch {i+1}")
                        start_time = time.time()
                        completion_response = chain.invoke({
                            'context': f'{context}',
                        })
                        elapsed_time = time.time() - start_time
                        logger.info(f"[DB-DEBUG] LLM response received in {elapsed_time:.2f} seconds for batch {i+1}")

                        # Extract the token usage from the response metadata
                        if 'response_metadata' in completion_response and 'token_usage' in completion_response.response_metadata:
                            token_usage = completion_response.response_metadata['token_usage']
                            prompt_tokens = token_usage.get('prompt_tokens', 0)
                            completion_tokens = token_usage.get('completion_tokens', 0)
                            total_tokens = token_usage.get('total_tokens', 0)

                            logger.info(f"[DB-DEBUG] Token usage - prompt: {prompt_tokens}, completion: {completion_tokens}, total: {total_tokens}")

                            # Calculate the cost for input, output, and total
                            input_cost = prompt_tokens * input_cost_per_token
                            output_cost = completion_tokens * output_cost_per_token
                            total_cost = input_cost + output_cost
                        else:
                            logger.warning(f"[DB-DEBUG] Token usage information not available in response")
                            prompt_tokens = 0
                            completion_tokens = 0
                            total_tokens = 0
                            input_cost = 0
                            output_cost = 0
                            total_cost = 0

                        # Append the result to the results array
                        results_array.append({
                            'inputCost': input_cost,
                            'outputCost': output_cost,
                            'totalCost': total_cost,
                            'answer': completion_response.content,
                            'promptTokens': prompt_tokens,
                            'completionTokens': completion_tokens,
                            'totalTokens': total_tokens,
                        })
                        logger.info(f"[DB-DEBUG] Result added for batch {i+1}")

                    except Exception as e:
                        logger.error(f"[DB-DEBUG] Error processing batch {i+1}: {str(e)}", exc_info=True)
                        # Add an error result
                        results_array.append({
                            'inputCost': 0,
                            'outputCost': 0,
                            'totalCost': 0,
                            'answer': f"Error processing this part of the data: {str(e)}",
                            'promptTokens': 0,
                            'completionTokens': 0,
                            'totalTokens': 0,
                        })

        # Concatenate the answers, calculate the sum of the costs, and calculate the sum of the tokens
        logger.info(f"[DB-DEBUG] Concatenating answers and calculating summaries")
        concatenated_answers = concatenate_answers(results_array, res_type)
        cost_summary = calculate_sum(results_array)
        token_summary = calculate_token_sum(results_array)

        logger.info(f"[DB-DEBUG] Response generation complete, answer length: {len(concatenated_answers)} characters")
        return concatenated_answers, cost_summary, token_summary, concatenated_answers

    except Exception as e:
        logger.error(f"[DB-DEBUG] Error in generate_response_content_admin: {str(e)}", exc_info=True)
        raise


def generate_response_content_text(prompt, text_data, res_type):
    """
    Function to generate a response content for admin based on a given prompt, res_type, and full text data.
    It uses the ChatPromptTemplate from langchain to create a prompt and the ChatOpenAI to generate the response.
    Makes a single LLM call with the full text instead of processing chunks.

    Args:
        prompt (str): The prompt string.
        text_data (str): The full text data as a string.
        res_type (str): The response type string.

    Returns:
        str: The response content from the LLM.
    """
    logger.info(f"Inside generate_response_content_text")
    logger.info(f"Full text length: {len(text_data)} characters")

    # Initialize the ChatOpenAI with the specified model and temperature
    llm = llm_factory.get_llm(openai_admin_llm, openai_admin_model)

    logger.info(f"ChatOpenAI initialized")

    prompt_template = ChatPromptTemplate.from_template(
        f"""
        You are an AI assistant, answer only from the given context.
        Question : {prompt}
        {{context}}
        """
    )

    logger.info(f"Prompt template created")

    # Create a runnable sequence using the prompt and ChatOpenAI llm
    chain = prompt_template | llm

    # Make a single LLM call with the full text
    logger.info(f"Making single LLM call with full text")
    completion_response = chain.invoke({
        'context': f'{text_data}',
    })

    logger.info(f"LLM response received, length: {len(completion_response.content)} characters")

    # Return the response content directly
    return completion_response.content


def generate_mcq_explanation(prompt, temp_chat_history, que_type, user_query, language, temp_chat_history_app):
    context = prompt
    llm = llm_factory.get_llm(openai_llm, openai_model)

    contextualize_q_prompt = llm_factory.create_qa_prompt(contextualize_q_system_prompt)

    documents = [Document(page_content=context,)]

    vectorstore = InMemoryVectorStore.from_documents(
        documents=documents, embedding=OpenAIEmbeddings(api_key=OPENAI_API_KEY_ADMIN)
    )
    retriever = vectorstore.as_retriever()
    history_aware_retriever = create_history_aware_retriever(llm, retriever, contextualize_q_prompt)

    sys_prompt = ""
    if que_type == "explain":
        sys_prompt = mcq_exp_sys_prompt
    elif que_type == "similarMCQ":
        sys_prompt = mcq_qa_sys_prompt
    elif que_type == "hint":
        sys_prompt = hint_qa_sys_prompt
    elif que_type == "explainQA":
        sys_prompt = qa_explanation
    elif que_type == "userInput":
        sys_prompt = f"""{prompt}
                    {user_query}
                    """

    qa_system_prompt = """
                Template:
                {context}
                """

    res_lang = "English"
    if language is not None and language != "":
        res_lang = language
        sys_prompt += f"""The response should be in {res_lang} language."""
    elif language == "":
        sys_prompt += f"""The response should be in {res_lang} language."""

    qa_prompt = llm_factory.create_qa_prompt(qa_system_prompt)

    question_answer_chain = create_stuff_documents_chain(llm, qa_prompt)
    rag_chain = create_retrieval_chain(history_aware_retriever, question_answer_chain)

    if temp_chat_history_app:
        new_chat_history = [
            HumanMessage(content=item['user']) if key == 'user' else AIMessage(content=item['ai'])
            for item in temp_chat_history_app for key in item]
    elif temp_chat_history:
        new_chat_history = [
            HumanMessage(content=item['user']) if key == 'user' else AIMessage(content=item['ai'])
            for item in temp_chat_history for key in item]
    else:
        new_chat_history = []

    chat_history = new_chat_history
    question = sys_prompt
    ai_msg_1 = rag_chain.invoke({"input": question, "chat_history": chat_history})
    chat_history.extend([HumanMessage(content=question), ai_msg_1["answer"]])
    response = ai_msg_1
    return {
        "answer": response["answer"],
        "query": prompt
    }


def handle_tts_data(content):

    llm = llm_factory.get_llm(openai_llm, openai_model)

    logger.info(f"ChatOpenAI initialized")

    prompt_template = ChatPromptTemplate.from_template(
        f"""
        Convert the following text into SSML (Speech Synthesis Markup Language) for Google Text-to-Speech,
        while following the below mentioned rules (Do not deviate from these rules):
        1. Wrap each word with a <mark> tag where the name attribute matches the word itself.
        2. The output should follow the pattern: <mark name='word' />word.
        3. Use proper SSML structure with <speak> for the root, <p> for paragraphs, and <ul> for lists where necessary.
        4. Always covert Latex formula into human readable text.
        5. Do not output any explanatory text, comments, or additional descriptions—only the SSML structure.

        Text:
        {{context}}
        """
    )

    chain = prompt_template | llm

    completion_response = chain.invoke({
        'context': f'{content}',
    })
    return completion_response.content


